#!/usr/bin/env node

const { execSync } = require('child_process');

function execCommand(command, options = {}) {
    try {
        // 在Windows系统上，SVN输出通常使用GBK编码
        // 我们需要使用buffer模式获取原始数据，然后手动转换编码
        const encoding = process.platform === 'win32' ? 'buffer' : 'utf8';

        const result = execSync(command, {
            encoding: encoding,
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options
        });

        let output = result;

        // 如果是Windows系统且返回的是buffer，需要转换编码
        if (process.platform === 'win32' && Buffer.isBuffer(result)) {
            // 尝试使用iconv-lite转换编码
            try {
                const iconv = require('iconv-lite');
                // 先尝试GBK编码
                output = iconv.decode(result, 'gbk');

                // 如果仍然有乱码，尝试其他编码
                if (output.includes('?') || output.includes('�')) {
                    // 尝试UTF-8编码
                    output = iconv.decode(result, 'utf8');

                    // 如果还是有问题，尝试GB2312
                    if (output.includes('?') || output.includes('�')) {
                        output = iconv.decode(result, 'gb2312');
                    }
                }

                // 清理无法显示的字符
                output = output.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

            } catch (iconvError) {
                // 如果iconv-lite不可用，回退到默认的toString()
                console.log('警告: 无法使用iconv-lite转换编码，可能出现乱码。建议安装: npm install iconv-lite');
                output = result.toString('utf8');
            }
        }

        return { success: true, output: output };
    } catch (error) {
        let errorOutput = error.stdout || error.stderr || '';

        // 同样处理错误输出的编码
        if (process.platform === 'win32' && Buffer.isBuffer(errorOutput)) {
            try {
                const iconv = require('iconv-lite');
                errorOutput = iconv.decode(errorOutput, 'gbk');
            } catch (iconvError) {
                errorOutput = errorOutput.toString('utf8');
            }
        }

        return {
            success: false,
            error: error.message,
            output: errorOutput
        };
    }
}

console.log('测试SVN编码修复...');
console.log('='.repeat(40));

// 测试SVN info命令
console.log('测试 svn info 命令:');
const infoResult = execCommand('svn info .', { silent: true });
if (infoResult.success) {
    console.log('✅ SVN info 命令执行成功，输出正常');
    console.log('输出内容:');
    console.log(infoResult.output);
} else {
    console.log('❌ SVN info 命令执行失败');
    console.log('错误:', infoResult.error);
}

console.log('\n' + '='.repeat(40));

// 测试SVN log命令
console.log('测试 svn log 命令 (最近3个版本):');
const logResult = execCommand('svn log -l 3 .', { silent: true });
if (logResult.success) {
    console.log('✅ SVN log 命令执行成功，输出正常');
    console.log('输出内容:');
    console.log(logResult.output);
} else {
    console.log('❌ SVN log 命令执行失败');
    console.log('错误:', logResult.error);
}

console.log('\n测试完成！');
