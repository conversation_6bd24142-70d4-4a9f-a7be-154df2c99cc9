#!/usr/bin/env node

// 测试提交信息替换功能
function replaceHistoryInCommitMessage(originalMessage, customMessage) {
    // 查找版本号变更部分和影响分析部分
    const versionChangePattern = /(版本号变更 \(共\d+个版本\):[^【]*?)(\[r\d+:[^\]]*\])*?(?=【影响分析】)/;
    const match = originalMessage.match(versionChangePattern);

    if (match) {
        // 找到版本号变更部分，替换历史记录
        const beforeVersionChange = originalMessage.substring(0, match.index + match[1].length);
        const afterHistory = originalMessage.substring(originalMessage.indexOf('【影响分析】'));

        // 构建新的提交信息：版本号变更部分 + 用户输入内容 + 影响分析部分
        return `${beforeVersionChange}，${customMessage} ${afterHistory}`;
    }

    // 如果没有找到预期的格式，尝试简单的替换方式
    const simplePattern = /(版本号变更[^【]*?)【影响分析】/;
    const simpleMatch = originalMessage.match(simplePattern);

    if (simpleMatch) {
        const beforePart = originalMessage.substring(0, simpleMatch.index + simpleMatch[1].length);
        const afterPart = originalMessage.substring(originalMessage.indexOf('【影响分析】'));
        return `${beforePart}，${customMessage} ${afterPart}`;
    }

    // 如果都没有找到，直接返回原始信息
    return originalMessage;
}

// 测试数据
const originalCommitMessage = `【其他】【更改描述】自动提交merged from https://cloudgroup.mindray.com/repos/2202/01-瑞影云++/03-Software/1-Source/08-feature/smart_ed_teaching/trunk_vue 版本号变更 (共1个版本):r32809[r32809: 功能描述：测试合并代码功能，影响分析：测试合并代码功能，测试建议：测试合并代码功能，更改描述：测试合并代码功能]【影响分析】合并trunk_vue主干代码到当前分支，更新相关功能模块【测试建议】验证合并后的功能是否正常，检查是否有冲突或异常`;

const customMessage = "用户输入的自定义内容：修复了登录模块的bug，优化了性能";

console.log('原始提交信息:');
console.log('-'.repeat(80));
console.log(originalCommitMessage);
console.log('-'.repeat(80));

console.log('\n用户输入的自定义内容:');
console.log(customMessage);

console.log('\n替换后的提交信息:');
console.log('-'.repeat(80));
const newCommitMessage = replaceHistoryInCommitMessage(originalCommitMessage, customMessage);
console.log(newCommitMessage);
console.log('-'.repeat(80));

console.log('\n说明: 历史记录部分已被用户输入的内容替换，但保留了版本号变更和影响分析部分！');
