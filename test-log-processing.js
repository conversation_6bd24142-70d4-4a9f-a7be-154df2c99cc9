#!/usr/bin/env node

// 测试日志处理功能
function processCommitLogs(commitLogs) {
    if (!commitLogs || commitLogs.length === 0) {
        return '';
    }

    // 按版本号分组处理日志
    const revisionGroups = {};

    commitLogs.forEach(log => {
        // 去除所有换行符，将多行内容合并为一行
        let cleanMessage = log.message.replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();

        // 移除中文括号及其内容
        cleanMessage = cleanMessage.replace(/【[^】]*】/g, '');

        // 清理多余的空格
        cleanMessage = cleanMessage.replace(/\s+/g, ' ').trim();

        if (cleanMessage) {
            if (!revisionGroups[log.revision]) {
                revisionGroups[log.revision] = new Set(); // 使用Set自动去重
            }

            // 先按数字序号分割（如"1、", "2、"等）
            const numberedParts = cleanMessage.split(/(?=\d+、)/).filter(part => part.trim());

            if (numberedParts.length > 1) {
                // 如果有数字序号，按序号分割处理
                numberedParts.forEach(part => {
                    part = part.trim();
                    if (part) {
                        // 移除开头的数字序号（如"1、"）
                        const content = part.replace(/^\d+、\s*/, '').trim();
                        if (content) {
                            revisionGroups[log.revision].add(content);
                        }
                    }
                });
            } else {
                // 如果没有数字序号，尝试按重复的短语分割
                // 先尝试找到重复的模式
                const phrases = [];
                let remainingMessage = cleanMessage;

                // 查找重复的短语模式
                const words = cleanMessage.split(/\s+/);
                const phraseCounts = {};

                // 检查2-5个词的短语
                for (let len = 2; len <= Math.min(5, words.length); len++) {
                    for (let i = 0; i <= words.length - len; i++) {
                        const phrase = words.slice(i, i + len).join(' ');
                        phraseCounts[phrase] = (phraseCounts[phrase] || 0) + 1;
                    }
                }

                // 找到出现次数大于1的短语
                const repeatedPhrases = Object.keys(phraseCounts).filter(phrase => phraseCounts[phrase] > 1);

                if (repeatedPhrases.length > 0) {
                    // 按长度排序，优先处理长短语
                    repeatedPhrases.sort((a, b) => b.length - a.length);

                    const uniquePhrases = new Set();
                    repeatedPhrases.forEach(phrase => {
                        uniquePhrases.add(phrase);
                    });

                    // 如果找到重复短语，将它们作为独立项目添加
                    let index = 1;
                    uniquePhrases.forEach(phrase => {
                        revisionGroups[log.revision].add(phrase);
                    });
                } else {
                    // 如果没有找到重复模式，直接添加整个消息
                    revisionGroups[log.revision].add(cleanMessage);
                }
            }
        }
    });

    // 构建格式化的日志字符串
    const formattedLogs = [];
    Object.keys(revisionGroups).forEach(revision => {
        const messagesSet = revisionGroups[revision];
        if (messagesSet.size === 0) {
            return;
        }

        const uniqueMessages = Array.from(messagesSet);

        if (uniqueMessages.length === 1) {
            formattedLogs.push(`[r${revision}: ${uniqueMessages[0]}]`);
        } else {
            const numberedMessages = uniqueMessages.map((msg, index) => `${index + 1}、${msg}`).join('，');
            formattedLogs.push(`[r${revision}: ${numberedMessages}]`);
        }
    });

    return formattedLogs.join('');
}

// 测试数据
const testLogs = [
    {
        revision: '32805',
        message: '1、【其他】 【功能描述】 测试合并代码功能 【影响分析】 测试合并代码功能 【测试建议】 测试合并代码功能 【更改描述】 测试合并代码功能'
    },
    {
        revision: '32806',
        message: '【其他】【功能描述】测试合并代码功能2【测试建议】测试合并代码功能2【更改描述】测试合并代码功能'
    },
    {
        revision: '32807',
        message: '1、【功能描述】修复bug 2、【测试建议】测试功能 3、【其他】优化性能'
    }
];

console.log('原始日志:');
testLogs.forEach(log => {
    console.log(`r${log.revision}: ${log.message}`);
});

console.log('\n处理后的日志:');
const result = processCommitLogs(testLogs);
console.log(result);

console.log('\n期望结果应该类似:');
console.log('[r32805: 1、测试合并代码功能][r32806: 1、测试合并代码功能2，2、测试合并代码功能]');
