#!/usr/bin/env node

// 测试日志处理功能
function processCommitLogs(commitLogs) {
    if (!commitLogs || commitLogs.length === 0) {
        return '';
    }

    // 处理每个提交日志
    const processedMessages = [];
    
    commitLogs.forEach(log => {
        // 去除所有换行符，将多行内容合并为一行
        let cleanMessage = log.message.replace(/\r?\n/g, ' ').replace(/\s+/g, ' ').trim();
        
        // 移除中文括号及其内容，但保留括号后面的内容
        cleanMessage = cleanMessage.replace(/【[^】]*】/g, '');
        
        // 清理多余的空格
        cleanMessage = cleanMessage.replace(/\s+/g, ' ').trim();
        
        // 如果处理后的消息不为空，添加到结果中
        if (cleanMessage) {
            processedMessages.push({
                revision: log.revision,
                message: cleanMessage
            });
        }
    });

    // 去重处理
    const uniqueMessages = [];
    const seenMessages = new Set();
    
    processedMessages.forEach(item => {
        if (!seenMessages.has(item.message)) {
            seenMessages.add(item.message);
            uniqueMessages.push(item);
        }
    });

    // 构建最终的日志字符串
    if (uniqueMessages.length === 0) {
        return '';
    }

    // 按版本号分组，每个版本号列出其对应的日志
    const revisionGroups = {};
    uniqueMessages.forEach(item => {
        if (!revisionGroups[item.revision]) {
            revisionGroups[item.revision] = [];
        }
        revisionGroups[item.revision].push(item.message);
    });

    // 构建格式化的日志字符串
    const formattedLogs = [];
    Object.keys(revisionGroups).forEach(revision => {
        const messages = revisionGroups[revision];
        if (messages.length === 1) {
            formattedLogs.push(`[r${revision}: ${messages[0]}]`);
        } else {
            const numberedMessages = messages.map((msg, index) => `${index + 1}、${msg}`).join('，');
            formattedLogs.push(`[r${revision}: ${numberedMessages}]`);
        }
    });

    return formattedLogs.join('');
}

// 测试数据
const testLogs = [
    {
        revision: '32805',
        message: '1、【其他】 【功能描述】 测试合并代码功能 【影响分析】 测试合并代码功能 【测试建议】 测试合并代码功能 【更改描述】 测试合并代码功能'
    },
    {
        revision: '32806',
        message: '【其他】【功能描述】测试合并代码功能2【测试建议】测试合并代码功能2【更改描述】测试合并代码功能'
    }
];

console.log('原始日志:');
testLogs.forEach(log => {
    console.log(`r${log.revision}: ${log.message}`);
});

console.log('\n处理后的日志:');
const result = processCommitLogs(testLogs);
console.log(result);

console.log('\n期望结果应该类似:');
console.log('[r32805: 1、测试合并代码功能][r32806: 1、测试合并代码功能2，2、测试合并代码功能]');
